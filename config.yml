global-settings:
  enabled: true
  allowed-commands: /list, /pl
  update-notification: true
  prefix: '&a[MobArena] '
  pet-items:
    wolf: bone
classes:
  # ========== 基础职业 ==========
  Knight:
    items: diamond_sword, potion:instant_heal:3
    armor: iron_helmet, iron_chestplate, iron_leggings, iron_boots
  Tank:
    items: iron_sword, potion:instant_heal:2
    armor: diamond_helmet, diamond_chestplate, diamond_leggings, diamond_boots
    effects: slow, health_boost
  Archer:
    items: wooden_sword, bow, arrow:256, potion:instant_heal:3, bone
    armor: leather_helmet, leather_chestplate, leather_leggings, leather_boots
    effects: speed
  Chemist:
    items: stone_sword, splash_potion:instant_damage:30, splash_potion:poison:8, splash_potion:instant_heal:20,
      potion:instant_heal:3
    armor: chainmail_helmet, chainmail_chestplate, chainmail_leggings, chainmail_boots
    effects: speed:1
  Oddjob:
    items: stone_sword, flint_and_steel, netherrack:2, tnt:4, potion:instant_heal:3
    armor: leather_helmet, leather_chestplate, leather_leggings, leather_boots

  # ========== 新增职业 ==========
  Paladin:
    items: netherite_sword, shield, potion:instant_heal:5, golden_apple:2, totem_of_undying
    armor: netherite_helmet, netherite_chestplate, netherite_leggings, netherite_boots
    effects: resistance:2, health_boost:2, regeneration:1

  Assassin:
    items: netherite_sword, iron_sword, potion:instant_heal:4, ender_pearl:8, potion:invisibility:3
    armor: leather_helmet, leather_chestplate, leather_leggings, leather_boots
    effects: speed:3, jump_boost:2, night_vision
  Mage:
    items: blaze_rod, potion:instant_heal:4, splash_potion:harming:20, splash_potion:slowness:15,
      lingering_potion:wither:10, ender_pearl:5
    armor: golden_helmet, golden_chestplate, golden_leggings, golden_boots
    effects: speed:2

  Berserker:
    items: netherite_axe, iron_axe, potion:instant_heal:3, potion:strength:10
    armor: iron_helmet, chainmail_chestplate, chainmail_leggings, iron_boots
    effects: strength:3, speed:2, resistance:1

  Ranger:
    items: crossbow, bow, arrow:512, spectral_arrow:64, potion:instant_heal:4,
      cooked_beef:8
    armor: leather_helmet, chainmail_chestplate, leather_leggings, leather_boots
    effects: speed:2, night_vision
  Necromancer:
    items: wither_skeleton_skull, bone:16, rotten_flesh:8, potion:instant_heal:3,
      splash_potion:wither:12, lingering_potion:poison:15
    armor: leather_helmet, leather_chestplate, leather_leggings, leather_boots
    effects: night_vision

  Monk:
    items: stick, potion:instant_heal:6, golden_apple:4, bread:12
    armor: leather_helmet, leather_chestplate, leather_leggings, leather_boots
    effects: speed:3, jump_boost:3, regeneration:2

  Pyromancer:
    items: fire_charge:20, blaze_rod, flint_and_steel, lava_bucket:2, potion:instant_heal:3,
      splash_potion:instant_damage:15
    armor: golden_helmet, golden_chestplate, golden_leggings, golden_boots
    effects: fire_resistance, speed:1

  Cryomancer:
    items: ice:16, packed_ice:8, blue_ice:4, snowball:64, potion:instant_heal:4,
      splash_potion:slowness:20
    armor: leather_helmet, leather_chestplate, leather_leggings, leather_boots
    effects: speed:2, water_breathing

  Engineer:
    items: redstone:32, redstone_torch:16, piston:8, dispenser:4, tnt:8,
      crossbow, potion:instant_heal:3
    armor: iron_helmet, iron_chestplate, iron_leggings, iron_boots
    effects: haste:2

  Druid:
    items: wooden_sword, bone_meal:16, wheat_seeds:32, apple:8, sweet_berries:16,
      potion:instant_heal:5, potion:regeneration:8
    armor: leather_helmet, leather_chestplate, leather_leggings, leather_boots
    effects: regeneration:1
  Warlock:
    items: netherite_sword, nether_star, soul_sand:8,
      potion:instant_heal:2, splash_potion:wither:15, lingering_potion:harming:25
    armor: netherite_helmet, netherite_chestplate, netherite_leggings, netherite_boots
    effects: strength:2, speed:1

  Bard:
    items: note_block:4, jukebox, music_disc_cat, potion:instant_heal:4,
      splash_potion:regeneration:12, bread:8
    armor: golden_helmet, golden_chestplate, golden_leggings, golden_boots
    effects: speed:2, jump_boost:1

  Alchemist:
    items: brewing_stand:2, cauldron, potion:instant_heal:8, splash_potion:instant_heal:20,
      lingering_potion:regeneration:15, splash_potion:strength:10, golden_apple:3
    armor: leather_helmet, leather_chestplate, leather_leggings, leather_boots
    effects: regeneration:1

  Summoner:
    items: bone:16, lead:8, potion:instant_heal:4, ender_pearl:3,
      rotten_flesh:8, spider_eye:4
    armor: leather_helmet, chainmail_chestplate, chainmail_leggings, leather_boots
    effects: speed:1

  Gladiator:
    items: trident, netherite_sword, shield, potion:instant_heal:4,
      golden_apple:2
    armor: iron_helmet, diamond_chestplate, iron_leggings, iron_boots
    effects: strength:2, resistance:1, speed:1
arenas:
  default:
    settings:
      prefix: ''
      world: world
      enabled: true
      protect: true
      entry-fee: ''
      default-class: ''
      clear-wave-before-next: false
      clear-boss-before-next: false
      clear-wave-before-boss: true
      clear-wave-leeway: 0
      soft-restore: false
      soft-restore-drops: false
      require-empty-inv-join: false
      require-empty-inv-spec: false
      pvp-enabled: false
      monster-infight: false
      allow-teleporting: false
      monster-teleporting: false
      spectate-on-death: true
      auto-leave-on-end: false
      share-items-in-arena: true
      min-players: 0
      max-players: 0
      max-join-distance: 0
      join-interrupt-timer: 0
      first-wave-delay: 5
      next-wave-delay: 0
      wave-interval: 15
      final-wave: 0
      spawnpoint-min-distance: 0
      spawnpoint-max-distance: 15
      monster-limit: 100
      monster-exp: false
      keep-exp: false
      food-regen: false
      lock-food-level: true
      player-time-in-arena: world
      auto-ignite-tnt: false
      auto-ignite-fuse: 80
      auto-start-timer: 0
      start-delay-timer: 0
      auto-ready: false
      use-class-chests: false
      arena-warp-offset: 0
      boss-health-bar: boss-bar
      display-waves-as-level: false
      display-timer-as-level: false
      use-scoreboards: true
      isolated-chat: false
      announcer-type: title
      global-join-announce: false
      global-end-announce: false
      show-death-messages: true
    waves:
      recurrent:
        def1:
          type: default
          priority: 1
          frequency: 1
          monsters:
            zombies: 10
            skeletons: 10
            spiders: 10
            creepers: 10
            wolves: 10
        spec1:
          type: special
          priority: 2
          frequency: 4
          monsters:
            powered-creepers: 10
            slimes-big: 10
            angry-wolves: 10
            blazes: 10
      single:
        swarm1:
          type: swarm
          wave: 6
          monster: slime
          amount: low
        boss1:
          type: boss
          wave: 10
          monster: cave_spider
          health: high
          abilities: arrows, root-target, throw-nearby
    rewards:
      waves:
        # ========== 每波次固定奖励 ==========
        every:
          '2': bread:3, cooked_beef:2, apple:2
          '3': feather:5, bone:8, stick:6, arrow:16
          '4': cobblestone:8, dirt:6, gravel:4
          '5': coal:12, iron_ingot:4, gold_ingot:2
          '6': leather:6, string:8, white_wool:4
          '7': redstone:8, gunpowder:6, slime_ball:4
          '8': emerald:2, lapis_lazuli:6, quartz:4
          '9': blaze_powder:3, ender_pearl:2, ghast_tear:1
          '10': diamond:3, netherite_scrap:1, ancient_debris:1
          '12': enchanted_book:1, experience_bottle:8, golden_apple:2
          '15': totem_of_undying:1, elytra:1, shulker_shell:2
          '20': nether_star:1, dragon_egg:1, beacon:1

        # ========== 特定波次后奖励 ==========
        after:
          '3': wooden_sword, wooden_pickaxe, wooden_axe
          '5': stone_sword, stone_pickaxe, stone_axe
          '7': iron_sword, iron_pickaxe, iron_axe, iron_shovel
          '10': diamond_sword, diamond_pickaxe, diamond_axe
          '12': bow, crossbow, trident
          '15': netherite_sword, netherite_pickaxe, netherite_axe
          '18': enchanted_book, enchanted_book
          '20': enchanted_book, enchanted_book
          '25': netherite_sword, netherite_axe
          '30': netherite_sword, bow
          '35': diamond_helmet, diamond_chestplate
          '40': netherite_helmet, netherite_chestplate
          '45': elytra, trident
          '50': netherite_sword, bow

        # ========== 阶段性大奖励 ==========
        tiers:
          '5': diamond:5, emerald:8, gold_ingot:15
          '10': diamond:10, netherite_ingot:2, enchanted_golden_apple:3
          '15': diamond:20, netherite_ingot:5, totem_of_undying:2, beacon:1
          '20': diamond:30, netherite_ingot:8, dragon_egg:1, nether_star:2
          '25': diamond:50, netherite_ingot:12, enchanted_golden_apple:5, elytra:1
          '30': diamond_block:10, netherite_block:3, beacon:2, shulker_box:4
          '35': diamond_block:15, netherite_block:5, dragon_head:1, end_crystal:4
          '40': diamond_block:25, netherite_block:8, wither_skeleton_skull:3, conduit:2
          '45': diamond_block:35, netherite_block:12, music_disc_pigstep:1, heart_of_the_sea:2
          '50': diamond_block:50, netherite_block:20, enchanted_golden_apple:10, totem_of_undying:5
          '60': emerald_block:20, netherite_block:30, beacon:5, shulker_box:8
          '70': emerald_block:35, netherite_block:45, dragon_egg:2, end_portal_frame:4
          '80': emerald_block:50, netherite_block:64, nether_star:5, conduit:5
          '90': emerald_block:64, netherite_block:64, totem_of_undying:10, elytra:3
          '100': emerald_block:64, netherite_block:64, beacon:10, dragon_egg:3, nether_star:10
    coords:
      spawnpoints: {}
      containers: {}
    class-limits:
      Oddjob: -1
      My Items: -1
      Knight: -1
      Archer: -1
      Tank: -1
      Chemist: -1
  雕像竞技场:
    settings:
      prefix: 试炼
      world: world
      enabled: true
      protect: true
      entry-fee: ''
      default-class: ''
      clear-wave-before-next: true
      clear-boss-before-next: true
      clear-wave-before-boss: true
      clear-wave-leeway: 15
      soft-restore: true
      soft-restore-drops: false
      require-empty-inv-join: false
      require-empty-inv-spec: false
      pvp-enabled: false
      monster-infight: false
      allow-teleporting: false
      monster-teleporting: false
      spectate-on-death: true
      auto-leave-on-end: false
      share-items-in-arena: true
      min-players: 0
      max-players: 0
      max-join-distance: 0
      join-interrupt-timer: 0
      first-wave-delay: 5
      next-wave-delay: 0
      wave-interval: 15
      final-wave: 99
      spawnpoint-min-distance: 0
      spawnpoint-max-distance: 15
      monster-limit: 100
      monster-exp: false
      keep-exp: false
      food-regen: false
      lock-food-level: true
      player-time-in-arena: world
      auto-ignite-tnt: false
      auto-ignite-fuse: 80
      auto-start-timer: 15
      start-delay-timer: 60
      auto-ready: true
      use-class-chests: false
      arena-warp-offset: 0
      boss-health-bar: boss-bar
      display-waves-as-level: false
      display-timer-as-level: false
      use-scoreboards: true
      isolated-chat: false
      announcer-type: chat
      global-join-announce: false
      global-end-announce: false
      show-death-messages: true
    waves:
      recurrent:
        # ========== 基础默认波次 ==========
        def1:
          type: default
          priority: 1
          frequency: 1
          monsters:
            zombies: 10
            skeletons: 10
            spiders: 10
            creepers: 10
            wolves: 10
            mm:ShadowWitherSkeleton: 4
            mm:ExtendedZombie: 8
            mm:SkeletonArcher: 8
        def2:
          type: default
          priority: 1
          frequency: 2
          monsters:
            BABY_DROWNED: 2
            CAVE_SPIDER: 3
            DROWNED: 4
            mm:VampireSpider: 3
        def3:
          type: default
          priority: 1
          frequency: 2
          monsters:
            HUSK: 2
            PIG_ZOMBIE: 3
            DROWNED: 4
            mm:skeletonking: 1
            mm:ExtendedZombie: 4
            mm:FrostStray: 4
        def4:
          type: default
          priority: 1
          frequency: 3
          wave: 7
          monsters:
            ENDERMAN: 3
            WITCH: 4
            BLAZE: 5
            GHAST: 2
            mm:ShadowSpiderKing: 2
        def5:
          type: default
          priority: 1
          frequency: 3
          wave: 10
          monsters:
            WITHER_SKELETON: 4
            PHANTOM: 6
            SHULKER: 3
            GUARDIAN: 4
            mm:InfernoKnight: 2
        def6:
          type: default
          priority: 2
          frequency: 4
          wave: 15
          monsters:
            ELDER_GUARDIAN: 2
            EVOKER: 3
            VINDICATOR: 5
            PILLAGER: 6
            mm:FrostGiant: 1
        def7:
          type: default
          priority: 2
          frequency: 5
          wave: 20
          monsters:
            RAVAGER: 3
            VEX: 8
            ILLUSIONER: 2
            PIGLIN_BRUTE: 4
            mm:ThunderShaman: 2
        def8:
          type: default
          priority: 2
          frequency: 6
          wave: 25
          monsters:
            HOGLIN: 4
            ZOGLIN: 3
            PIGLIN: 5
            ZOMBIFIED_PIGLIN: 6
            mm:VoidWalker: 1
        def9:
          type: default
          priority: 3
          frequency: 7
          wave: 30
          monsters:
            WARDEN: 1
            GOAT: 6
            AXOLOTL: 4
            GLOW_SQUID: 5
            mm:CorrosionGuardian: 2
        def10:
          type: default
          priority: 3
          frequency: 8
          wave: 35
          monsters:
            ALLAY: 8
            BEE: 10
            DOLPHIN: 4
            TURTLE: 3
            mm:StormLord: 2
        def11:
          type: default
          priority: 3
          frequency: 9
          wave: 40
          monsters:
            CAMEL: 5
            SNIFFER: 3
            BREEZE: 7
            BOGGED: 6
            mm:HellhoundAlpha: 2
        def12:
          type: default
          priority: 4
          frequency: 10
          wave: 45
          monsters:
            ARMADILLO: 8
            FROG: 6
            TADPOLE: 12
            STRIDER: 5
            mm:CrystalGuardian: 1
        def13:
          type: default
          priority: 4
          frequency: 11
          wave: 50
          monsters:
            POLAR_BEAR: 4
            PANDA: 3
            FOX: 6
            OCELOT: 5
            mm:ShadowAssassin: 3
        def14:
          type: default
          priority: 4
          frequency: 12
          wave: 55
          monsters:
            LLAMA: 6
            TRADER_LLAMA: 4
            HORSE: 5
            DONKEY: 4
            mm:DeepSeaKraken: 1
        def15:
          type: default
          priority: 5
          frequency: 13
          wave: 60
          monsters:
            MULE: 4
            ZOMBIE_HORSE: 3
            SKELETON_HORSE: 3
            PARROT: 8
            mm:BoneDragonLord: 1
        def16:
          type: default
          priority: 5
          frequency: 14
          wave: 65
          monsters:
            BAT: 15
            SILVERFISH: 12
            ENDERMITE: 10
            VEX: 8
            mm:SpiderGuardian: 4
        def17:
          type: default
          priority: 5
          frequency: 15
          wave: 70
          monsters:
            MOOSHROOM: 5
            COW: 6
            PIG: 7
            SHEEP: 8
            mm:SpiderMinion: 6
        def18:
          type: default
          priority: 6
          frequency: 16
          wave: 75
          monsters:
            CHICKEN: 10
            RABBIT: 8
            VILLAGER: 4
            WANDERING_TRADER: 2
            mm:VenomSpider: 5
        def19:
          type: default
          priority: 6
          frequency: 17
          wave: 80
          monsters:
            IRON_GOLEM: 3
            SNOW_GOLEM: 5
            CAT: 6
            SQUID: 8
            mm:ExecutionGuard: 3
        def20:
          type: default
          priority: 6
          frequency: 18
          wave: 85
          monsters:
            MAGMA_CUBE: 8
            SLIME: 10
            ZOMBIE_VILLAGER: 5
            BABY_ZOMBIE: 6
            mm:BloodMirrorClone: 4

        # ========== 特殊波次 ==========
        spec1:
          type: special
          priority: 2
          frequency: 4
          monsters:
            powered-creepers: 10
            slimes-big: 10
            angry-wolves: 10
            blazes: 10
            mm:ExtendedZombie: 4
        spec2:
          type: special
          priority: 3
          frequency: 5
          wave: 8
          monsters:
            MAGMA_CUBE: 15
            SLIME: 20
            mm:HellhoundAlpha: 3
        spec3:
          type: special
          priority: 3
          frequency: 6
          wave: 12
          monsters:
            IRON_GOLEM: 5
            SNOW_GOLEM: 8
            mm:CrystalGuardian: 2
        spec4:
          type: special
          priority: 4
          frequency: 7
          wave: 18
          monsters:
            ENDER_DRAGON: 1
            WITHER: 2
            mm:ShadowAssassin: 4
        spec5:
          type: special
          priority: 4
          frequency: 8
          wave: 22
          monsters:
            GIANT: 2
            ZOMBIE_HORSE: 4
            SKELETON_HORSE: 4
            mm:DeepSeaKraken: 1
        spec6:
          type: special
          priority: 5
          frequency: 9
          wave: 28
          monsters:
            STRIDER: 6
            TADPOLE: 12
            FROG: 8
            mm:BoneDragonLord: 1
        spec7:
          type: special
          priority: 5
          frequency: 10
          wave: 33
          monsters:
            CAMEL: 5
            SNIFFER: 3
            BREEZE: 7
            mm:SpiderQueen: 1
        spec8:
          type: special
          priority: 6
          frequency: 12
          wave: 40
          monsters:
            BOGGED: 8
            ARMADILLO: 6
            mm:WarwickExecutioner: 1
        spec9:
          type: special
          priority: 6
          frequency: 13
          wave: 45
          monsters:
            KILLER_BUNNY: 12
            ANGRY_BEE: 15
            mm:ShadowSpiderling: 8
        spec10:
          type: special
          priority: 7
          frequency: 14
          wave: 50
          monsters:
            EXPLODING_SHEEP: 10
            BABY_ZOMBIE: 8
            mm:FlameServant: 6
        spec11:
          type: special
          priority: 7
          frequency: 15
          wave: 55
          monsters:
            BABY_HUSK: 6
            BABY_DROWNED: 8
            mm:Hellhound: 10
        spec12:
          type: special
          priority: 8
          frequency: 16
          wave: 60
          monsters:
            BABY_PIGLIN: 8
            BABY_HOGLIN: 6
            mm:ShadowClone: 5
        spec13:
          type: special
          priority: 8
          frequency: 17
          wave: 65
          monsters:
            BABY_ZOGLIN: 6
            BABY_ZOMBIE_VILLAGER: 8
            mm:BoneDragon: 4
        spec14:
          type: special
          priority: 9
          frequency: 18
          wave: 70
          monsters:
            SLIME_HUGE: 4
            MAGMA_CUBE_HUGE: 3
            mm:LightningTotem: 6
        spec15:
          type: special
          priority: 9
          frequency: 19
          wave: 75
          monsters:
            SLIME_TINY: 20
            MAGMA_CUBE_TINY: 15
            mm:CrystalPillar: 8
        spec16:
          type: special
          priority: 10
          frequency: 20
          wave: 80
          monsters:
            WITHER_SKELETON: 8
            STRAY: 10
            mm:SpiderNestCore: 3
        spec17:
          type: special
          priority: 10
          frequency: 21
          wave: 85
          monsters:
            SKELETON: 12
            ZOMBIE: 10
            mm:DisguiseBlock: 6
        spec18:
          type: special
          priority: 11
          frequency: 22
          wave: 90
          monsters:
            CREEPER: 8
            POWERED_CREEPER: 4
            mm:GallowsDevice: 2
        spec19:
          type: special
          priority: 11
          frequency: 23
          wave: 95
          monsters:
            SPIDER: 10
            CAVE_SPIDER: 8
            mm:WebTrap: 12
        spec20:
          type: special
          priority: 12
          frequency: 25
          wave: 98
          monsters:
            ENDERMAN: 6
            SHULKER: 4
            mm:PoisonCircle: 8
      single:
        # ========== 群体波次 ==========
        swarm1:
          type: swarm
          wave: 4
          monster: mm:ElitePillager
          amount: low
        swarm2:
          type: swarm
          wave: 8
          monster: mm:BlazeSlime
          amount: medium
        swarm3:
          type: swarm
          wave: 16
          monster: mm:ThunderWitch
          amount: high
        swarm4:
          type: swarm
          wave: 24
          monster: mm:ShadowGuard
          amount: psycho
        swarm5:
          type: swarm
          wave: 12
          monster: slime
          amount: medium
        swarm6:
          type: swarm
          wave: 20
          monster: cave_spider
          amount: high
        swarm7:
          type: swarm
          wave: 28
          monster: silverfish
          amount: psycho
        swarm8:
          type: swarm
          wave: 36
          monster: endermite
          amount: high
        swarm9:
          type: swarm
          wave: 44
          monster: vex
          amount: psycho
        swarm10:
          type: swarm
          wave: 52
          monster: mm:ShadowSpiderling
          amount: psycho
        swarm11:
          type: swarm
          wave: 60
          monster: mm:Hellhound
          amount: high
        swarm12:
          type: swarm
          wave: 68
          monster: baby_zombie
          amount: psycho
        swarm13:
          type: swarm
          wave: 76
          monster: mm:FlameServant
          amount: medium
        swarm14:
          type: swarm
          wave: 84
          monster: mm:ShadowClone
          amount: high
        swarm15:
          type: swarm
          wave: 92
          monster: mm:BoneDragon
          amount: medium

        # ========== 补给波次 ==========
        supply1:
          type: supply
          wave: 6
          monsters:
            cow: 8
            pig: 6
            chicken: 10
          drops: cooked_beef, cooked_porkchop, cooked_chicken, bread:3, apple:2
        supply2:
          type: supply
          wave: 14
          monsters:
            sheep: 10
            rabbit: 8
          drops: cooked_mutton, cooked_rabbit, wool:4, leather:3
        supply3:
          type: supply
          wave: 22
          monsters:
            villager: 6
            wandering_trader: 2
          drops: emerald:2, gold_ingot:1, iron_ingot:3, diamond:1
        supply4:
          type: supply
          wave: 30
          monsters:
            horse: 4
            donkey: 3
            mule: 2
          drops: saddle, lead:2, hay_block:3, carrot:5
        supply5:
          type: supply
          wave: 38
          monsters:
            llama: 5
            trader_llama: 3
          drops: leather:4, wool:6, chest:1, string:8
        supply6:
          type: supply
          wave: 46
          monsters:
            cat: 8
            ocelot: 6
          drops: string:6, raw_fish:4, cooked_fish:3
        supply7:
          type: supply
          wave: 54
          monsters:
            parrot: 10
            bat: 12
          drops: feather:8, cookie:4, seeds:10
        supply8:
          type: supply
          wave: 62
          monsters:
            fox: 6
            wolf: 8
          drops: sweet_berries:6, bone:8, leather:4
        supply9:
          type: supply
          wave: 70
          monsters:
            panda: 4
            polar_bear: 3
          drops: bamboo:8, fish:6, honey_bottle:2
        supply10:
          type: supply
          wave: 78
          monsters:
            turtle: 6
            dolphin: 4
          drops: scute:3, kelp:8, prismarine_shard:4
        supply11:
          type: supply
          wave: 86
          monsters:
            bee: 12
            mooshroom: 4
          drops: honeycomb:6, honey_bottle:3, mushroom_stew:2
        supply12:
          type: supply
          wave: 94
          monsters:
            axolotl: 8
            glow_squid: 6
          drops: tropical_fish_bucket:1, glow_ink_sac:4, ink_sac:6
        # ========== 升级波次 ==========
        upgrade1:
          type: upgrade
          wave: 5
          upgrades:
            all: potion:instant_heal:2, bread:3
            Knight: iron_sword
            Archer: arrow:32
            Tank: iron_helmet
            Chemist: splash_potion:instant_heal:5
            Oddjob: tnt:1, flint_and_steel
        upgrade2:
          type: upgrade
          wave: 10
          upgrades:
            all: potion:instant_heal:3, golden_apple:1
            Knight:
              items: diamond_sword
              armor: iron_chestplate
            Archer:
              items: bow power:1
              armor: leather_chestplate protection:1
            Tank:
              armor: diamond_helmet, iron_chestplate
            Chemist: splash_potion:poison:8, splash_potion:instant_damage:5
            Oddjob: tnt:3, netherrack:4
        upgrade3:
          type: upgrade
          wave: 18
          upgrades:
            all: potion:instant_heal:4, golden_apple:2, ender_pearl:1
            Knight:
              items: diamond_sword sharpness:1
              armor: diamond_helmet, diamond_chestplate
            Archer:
              items: bow power:2;infinity:1
              armor: chainmail_helmet, chainmail_chestplate
            Tank:
              armor: diamond_helmet, diamond_chestplate, diamond_leggings
              effects: resistance:1
            Chemist:
              items: diamond_sword, splash_potion:instant_damage:10, splash_potion:poison:12
            Oddjob: tnt:5, fire_charge:8
        upgrade4:
          type: upgrade
          wave: 26
          upgrades:
            all: potion:instant_heal:5, golden_apple:3, totem_of_undying:1
            Knight:
              items: netherite_sword sharpness:2;knockback:1
              armor: diamond_helmet protection:1, diamond_chestplate protection:1
              effects: strength:1
            Archer:
              items: crossbow quick_charge:2;piercing:1, arrow:128
              armor: iron_helmet, iron_chestplate, iron_leggings
              effects: speed:1
            Tank:
              armor: netherite_helmet, netherite_chestplate, netherite_leggings, diamond_boots
              effects: resistance:2, health_boost:1
            Chemist:
              items: netherite_sword, splash_potion:instant_damage:15, splash_potion:wither:8
              effects: speed:1
            Oddjob: tnt:8, respawn_anchor:2, fire_charge:12
        upgrade5:
          type: upgrade
          wave: 34
          upgrades:
            all: enchanted_golden_apple:1, potion:instant_heal:6
            Knight:
              items: netherite_sword sharpness:3;fire_aspect:1;knockback:1
              armor: netherite_helmet protection:2, netherite_chestplate protection:2
              effects: strength:2, speed:1
            Archer:
              items: crossbow quick_charge:3;piercing:2;multishot:1, spectral_arrow:64
              armor: netherite_helmet, netherite_chestplate, iron_leggings
              effects: speed:2
            Tank:
              armor: netherite_helmet protection:3, netherite_chestplate protection:3, netherite_leggings protection:2, netherite_boots protection:1
              effects: resistance:3, health_boost:2, regeneration:1
            Chemist:
              items: netherite_sword sharpness:2, lingering_potion:instant_damage:20, lingering_potion:wither:12
              effects: speed:2, jump_boost:1
            Oddjob: tnt:12, end_crystal:3, fire_charge:20
        upgrade6:
          type: upgrade
          wave: 42
          upgrades:
            all: enchanted_golden_apple:2, potion:instant_heal:8, ender_pearl:3
            Knight:
              items: netherite_sword sharpness:4;fire_aspect:2;knockback:2;sweeping:1
              armor: netherite_helmet protection:3;unbreaking:2, netherite_chestplate protection:3;unbreaking:2
              effects: strength:3, speed:2, resistance:1
            Archer:
              items: crossbow quick_charge:3;piercing:3;multishot:1;unbreaking:2, tipped_arrow:128
              armor: netherite_helmet protection:2, netherite_chestplate protection:2, netherite_leggings protection:1
              effects: speed:3, jump_boost:1
            Tank:
              armor: netherite_helmet protection:4;unbreaking:3, netherite_chestplate protection:4;unbreaking:3, netherite_leggings protection:3;unbreaking:2, netherite_boots protection:2;unbreaking:2
              effects: resistance:4, health_boost:3, regeneration:2
            Chemist:
              items: netherite_sword sharpness:3;fire_aspect:1, lingering_potion:instant_damage:25, lingering_potion:wither:15
              effects: speed:3, jump_boost:2, night_vision
            Oddjob: tnt:16, end_crystal:5, respawn_anchor:4, fire_charge:30
        upgrade7:
          type: upgrade
          wave: 50
          upgrades:
            all: enchanted_golden_apple:3, potion:instant_heal:10, ender_pearl:5, elytra:1
            Knight:
              items: netherite_sword sharpness:5;fire_aspect:2;knockback:2;sweeping:2;unbreaking:3
              armor: netherite_helmet protection:4;unbreaking:3;thorns:1, netherite_chestplate protection:4;unbreaking:3;thorns:1
              effects: strength:4, speed:3, resistance:2, health_boost:1
            Archer:
              items: crossbow quick_charge:3;piercing:4;multishot:1;unbreaking:3, tipped_arrow:256
              armor: netherite_helmet protection:3;unbreaking:2, netherite_chestplate protection:3;unbreaking:2, netherite_leggings protection:2;unbreaking:2
              effects: speed:4, jump_boost:2, slow_falling:1
            Tank:
              armor: netherite_helmet protection:5;unbreaking:3;thorns:2, netherite_chestplate protection:5;unbreaking:3;thorns:2, netherite_leggings protection:4;unbreaking:3, netherite_boots protection:3;unbreaking:2
              effects: resistance:5, health_boost:4, regeneration:3, absorption:2
            Chemist:
              items: netherite_sword sharpness:4;fire_aspect:2;unbreaking:2, lingering_potion:instant_damage:30, lingering_potion:wither:20
              effects: speed:4, jump_boost:3, night_vision, water_breathing
            Oddjob: tnt:20, end_crystal:8, respawn_anchor:6, fire_charge:50, firework_rocket:16

        # ========== Boss波次 ==========
        boss1:
          type: boss
          wave: 16
          monster: mm:WitherBoss
          health: high
          abilities: arrows, root-target, throw-nearby
        boss2:
          type: boss
          wave: 24
          monster: PIG_ZOMBIE
          health: high
          abilities: arrows, root-target, throw-nearby
        boss3:
          type: boss
          wave: 32
          monster: husk
          health: high
          abilities: arrows, root-target, throw-nearby
        boss4:
          type: boss
          wave: 40
          monster: mm:EnhancedSkeleton
          health: high
          abilities: root-target, arrows, fetch-distant, fire-aura
          effects: speed:3:20, wither, increase_damage:1
          ability-interval: 5
          drops: lever, stone_button
        boss5:
          type: boss
          wave: 48
          monster: mm:SplittingCreeperKing
          health: high
          abilities: root-target, arrows, fetch-distant, fire-aura
          effects: speed:3:20, wither, increase_damage:1
          ability-interval: 5
          drops: lever, stone_button
        boss6:
          type: boss
          wave: 56
          monster: mm:StormWizard
          health: high
          abilities: root-target, arrows, fetch-distant, fire-aura
          effects: speed:3:20, wither, increase_damage:1
          ability-interval: 5
          drops: lever, stone_button
        boss7:
          type: boss
          wave: 61
          monster: mm:VoidDevourer
          health: high
          abilities: root-target, arrows, fetch-distant, fire-aura
          effects: speed:3:20, wither, increase_damage:1
          ability-interval: 5
          drops: lever, stone_button
        boss8:
          type: boss
          wave: 72
          monster: mm:FlameLord
          health: high
          abilities: root-target, arrows, fetch-distant, fire-aura
          effects: speed:3:20, wither, increase_damage:1
          ability-interval: 5
          drops: lever, stone_button
        boss9:
          type: boss
          wave: 79
          monster: mm:FrostTitan
          health: high
          abilities: root-target, arrows, fetch-distant, fire-aura
          effects: speed:3:20, wither, increase_damage:1
          ability-interval: 5
          drops: lever, stone_button
        boss10:
          type: boss
          wave: 87
          monster: mm:SkeletonKnightRider
          health: high
          abilities: root-target, arrows, fetch-distant, fire-aura
          effects: speed:3:20, wither, increase_damage:1
          ability-interval: 5
          drops: lever, stone_button
        boss11:
          type: boss
          wave: 93
          monster: mm:SpiderQueenRage
          health: high
          abilities: fire-aura, disorient-target, fireballs, throw-nearby
          effects: speed:3:20, wither, increase_damage:1
          ability-interval: 5
          drops: lever, stone_button
        boss12:
          type: boss
          wave: 99
          monster: mm:DisguiseMaster
          health: high
          abilities: root-target, arrows, fetch-distant, fire-aura
          effects: speed:3:20, wither, increase_damage:1
          ability-interval: 5
          drops: lever, stone_button
        # ========== 新增Boss波次 ==========
        boss13:
          type: boss
          wave: 7
          monster: mm:ShadowSpiderKing
          health: medium
          abilities: fire-aura, disorient-target, throw-nearby
          effects: speed:2, poison_resistance
          ability-interval: 4
          drops: spider_eye:3, string:5
        boss14:
          type: boss
          wave: 11
          monster: mm:InfernoKnight
          health: medium
          abilities: fire-aura, fireballs, throw-target
          effects: fire_resistance, strength:1
          ability-interval: 4
          drops: blaze_rod:2, fire_charge:4
        boss15:
          type: boss
          wave: 15
          monster: mm:FrostGiant
          health: high
          abilities: lightning-aura, throw-nearby, root-target
          effects: slowness:1, resistance:1
          ability-interval: 5
          drops: ice:8, packed_ice:4
        boss16:
          type: boss
          wave: 19
          monster: mm:ThunderShaman
          health: medium
          abilities: lightning-aura, chain-lightning, disorient-all
          effects: speed:2, jump_boost:1
          ability-interval: 3
          drops: gold_ingot:3, redstone:8
        boss17:
          type: boss
          wave: 23
          monster: mm:VoidWalker
          health: high
          abilities: warp-to-player, fetch-target, disorient-target
          effects: speed:3, invisibility:5
          ability-interval: 4
          drops: ender_pearl:4, obsidian:3
        boss18:
          type: boss
          wave: 27
          monster: mm:CorrosionGuardian
          health: medium
          abilities: arrows, poison-aura, root-target
          effects: water_breathing, poison_resistance
          ability-interval: 4
          drops: prismarine_shard:4, slime_ball:6
        boss19:
          type: boss
          wave: 31
          monster: mm:StormLord
          health: medium
          abilities: lightning-aura, throw-all, disorient-nearby
          effects: levitation:3, speed:2
          ability-interval: 3
          drops: feather:8, phantom_membrane:2
        boss20:
          type: boss
          wave: 35
          monster: mm:HellhoundAlpha
          health: high
          abilities: fire-aura, fetch-nearby, throw-target
          effects: fire_resistance, strength:2
          ability-interval: 4
          drops: bone:6, blaze_powder:4
        boss21:
          type: boss
          wave: 39
          monster: mm:CrystalGuardian
          health: high
          abilities: arrows, lightning-aura, root-target
          effects: resistance:2, glowing
          ability-interval: 5
          drops: amethyst_shard:6, quartz:4
        boss22:
          type: boss
          wave: 43
          monster: mm:ShadowAssassin
          health: medium
          abilities: warp-to-player, fetch-target, disorient-target
          effects: speed:4, invisibility:8
          ability-interval: 3
          drops: iron_sword:1, leather:4
        boss23:
          type: boss
          wave: 47
          monster: mm:DeepSeaKraken
          health: veryhigh
          abilities: throw-all, root-target, flood
          effects: water_breathing, resistance:1
          ability-interval: 5
          drops: ink_sac:8, prismarine_shard:5
        boss24:
          type: boss
          wave: 51
          monster: mm:BoneDragonLord
          health: veryhigh
          abilities: fireballs, lightning-aura, fetch-distant
          effects: fire_resistance, strength:2
          ability-interval: 4
          drops: bone:10, wither_skeleton_skull:1
        boss25:
          type: boss
          wave: 55
          monster: mm:SpiderQueen
          health: veryhigh
          abilities: fire-aura, disorient-all, throw-nearby
          effects: speed:2, poison_resistance
          ability-interval: 4
          drops: spider_eye:5, string:10
        boss26:
          type: boss
          wave: 59
          monster: wither
          health: psycho
          abilities: fireballs, lightning-aura, disorient-all
          effects: wither_resistance, strength:3
          ability-interval: 3
          drops: nether_star:1, wither_skeleton_skull:2
        boss27:
          type: boss
          wave: 63
          monster: ender_dragon
          health: psycho
          abilities: fireballs, throw-all, fetch-distant
          effects: levitation:2, resistance:2
          ability-interval: 4
          drops: dragon_egg:1, dragon_breath:5
        boss28:
          type: boss
          wave: 67
          monster: mm:WarwickExecutioner
          health: psycho
          abilities: fire-aura, lightning-aura, disorient-all, fetch-all
          effects: strength:4, resistance:3, speed:2
          ability-interval: 2
          drops: netherite_ingot:2, diamond:6
        boss29:
          type: boss
          wave: 71
          monster: warden
          health: psycho
          abilities: root-target, disorient-all, throw-nearby
          effects: darkness:10, strength:5
          ability-interval: 3
          drops: echo_shard:3, sculk:8
        boss30:
          type: boss
          wave: 75
          monster: elder_guardian
          health: psycho
          abilities: arrows, lightning-aura, root-target
          effects: mining_fatigue:5, water_breathing
          ability-interval: 4
          drops: prismarine_crystals:4, sponge:2
        boss31:
          type: boss
          wave: 80
          monster: ravager
          health: psycho
          abilities: throw-all, root-target, fire-aura
          effects: strength:4, resistance:2
          ability-interval: 3
          drops: saddle:1, emerald:8
        boss32:
          type: boss
          wave: 85
          monster: iron_golem
          health: psycho
          abilities: throw-nearby, root-target, lightning-aura
          effects: resistance:4, strength:3
          ability-interval: 4
          drops: iron_ingot:8, poppy:4
        boss33:
          type: boss
          wave: 90
          monster: giant
          health: psycho
          abilities: throw-all, disorient-all, fire-aura
          effects: strength:5, resistance:3, speed:1
          ability-interval: 2
          drops: rotten_flesh:20, bone:15
        boss34:
          type: boss
          wave: 95
          monster: mm:DisguiseMasterPhaseTwo
          health: psycho
          abilities: warp-to-player, fetch-all, disorient-all, fire-aura, lightning-aura
          effects: strength:5, resistance:4, speed:3, invisibility:10
          ability-interval: 1
          drops: totem_of_undying:1, netherite_scrap:4
    rewards:
      waves:
        # ========== 每波次固定奖励 ==========
        every:
          '2': bread:3, cooked_beef:2, apple:2
          '3': feather:5, bone:8, stick:6, arrow:16
          '4': cobblestone:8, dirt:6, gravel:4
          '5': coal:12, iron_ingot:4, gold_ingot:2
          '6': leather:6, string:8, white_wool:4
          '7': redstone:8, gunpowder:6, slime_ball:4
          '8': emerald:2, lapis_lazuli:6, quartz:4
          '9': blaze_powder:3, ender_pearl:2, ghast_tear:1
          '10': diamond:3, netherite_scrap:1, ancient_debris:1
          '12': enchanted_book:1, experience_bottle:8, golden_apple:2
          '15': totem_of_undying:1, elytra:1, shulker_shell:2
          '20': nether_star:1, dragon_egg:1, beacon:1

        # ========== 特定波次后奖励 ==========
        after:
          '3': wooden_sword, wooden_pickaxe, wooden_axe
          '5': stone_sword, stone_pickaxe, stone_axe
          '7': iron_sword, iron_pickaxe, iron_axe, iron_shovel
          '10': diamond_sword, diamond_pickaxe, diamond_axe
          '12': bow, crossbow, trident
          '15': netherite_sword, netherite_pickaxe, netherite_axe
          '18': enchanted_book, enchanted_book
          '20': enchanted_book, enchanted_book
          '25': netherite_sword, netherite_axe
          '30': netherite_sword, bow
          '35': diamond_helmet, diamond_chestplate
          '40': netherite_helmet, netherite_chestplate
          '45': elytra, trident
          '50': netherite_sword, bow

        # ========== 阶段性大奖励 ==========
        tiers:
          '5': diamond:5, emerald:8, gold_ingot:15
          '10': diamond:10, netherite_ingot:2, enchanted_golden_apple:3
          '15': diamond:20, netherite_ingot:5, totem_of_undying:2, beacon:1
          '20': diamond:30, netherite_ingot:8, dragon_egg:1, nether_star:2
          '25': diamond:50, netherite_ingot:12, enchanted_golden_apple:5, elytra:1
          '30': diamond_block:10, netherite_block:3, beacon:2, shulker_box:4
          '35': diamond_block:15, netherite_block:5, dragon_head:1, end_crystal:4
          '40': diamond_block:25, netherite_block:8, wither_skeleton_skull:3, conduit:2
          '45': diamond_block:35, netherite_block:12, music_disc_pigstep:1, heart_of_the_sea:2
          '50': diamond_block:50, netherite_block:20, enchanted_golden_apple:10, totem_of_undying:5
          '60': emerald_block:20, netherite_block:30, beacon:5, shulker_box:8
          '70': emerald_block:35, netherite_block:45, dragon_egg:2, end_portal_frame:4
          '80': emerald_block:50, netherite_block:64, nether_star:5, conduit:5
          '90': emerald_block:64, netherite_block:64, totem_of_undying:10, elytra:3
          '100': emerald_block:64, netherite_block:64, beacon:10, dragon_egg:3, nether_star:10
    coords:
      spawnpoints:
        108,92,-238: 108.5,92,-237.5,0.0,0.0,world
        90,92,-234: 90.5,92,-233.5,0.0,0.0,world
        100,98,-224: 100.5,98,-223.5,0.0,0.0,world
        99,85,-224: 99.5,85,-223.5,0.0,0.0,world
        88,95,-235: 88.5,95,-234.5,0.0,0.0,world
      containers:
        98,76,-242: 98,76,-242,0.0,0.0,world
        107,76,-229: 107,76,-229,0.0,0.0,world
        94,79,-224: 94,79,-224,0.0,0.0,world
        108,91,-230: 108,91,-230,0.0,0.0,world
        92,96,-228: 92,96,-228,0.0,0.0,world
        105,104,-230: 105,104,-230,0.0,0.0,world
        100,90,-222: 100,90,-222,0.0,0.0,world
        93,83,-239: 93,83,-239,0.0,0.0,world
        100,98,-240: 100,98,-240,0.0,0.0,world
      p1: 85,75,-246,0.0,0.0,world
      p2: 121,119,-209,0.0,0.0,world
      lobby: 100.5,109,-230.5,-95.64,0.0,world
      spectator: 101.5,109,-230.5,-89.03,0.0,world
      arena: 100.5,76,-232.5,-75.24,0.0,world
      exit: 48.5,83,-261.5,135.36,0.0,world
      l1: 100,75,-233,0.0,0.0,world
      l2: 100,108,-231,0.0,0.0,world
    class-limits:
      # ========== 基础职业 ==========
      Knight: -1
      Tank: -1
      Archer: -1
      Chemist: -1
      Oddjob: -1
      My Items: -1

      # ========== 新增职业 ==========
      Paladin: -1
      Assassin: -1
      Mage: -1
      Berserker: -1
      Ranger: -1
      Necromancer: -1
      Monk: -1
      Pyromancer: -1
      Cryomancer: -1
      Engineer: -1
      Druid: -1
      Warlock: -1
      Bard: -1
      Alchemist: -1
      Summoner: -1
      Gladiator: -1
