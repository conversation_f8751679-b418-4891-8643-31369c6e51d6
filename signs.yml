# MobArena 标牌模板配置
#
# 使用此文件为 MobArena 设置标牌模板。此文件中的所有模板都可以用于
# 三种标牌类型（info、join、leave）。
#
# 模板示例：
#
# 竞技场模板:
# - '<arena-name>'
# - '==============='
# - '点击标牌'
# - '加入竞技场！'
#
# 模板ID是'竞技场模板'，使用<arena-name>变量，会被翻译成竞技场名称。
# 要在名为'雕像竞技场'的竞技场中使用加入标牌模板，只需在游戏中放置
# 一个标牌，内容如下：
#
#   [MA]           <-- MobArena必需
#   雕像竞技场      <-- 竞技场名称
#   join          <-- 标牌类型
#   竞技场模板      <-- 模板（可选）
#
# 有关可用变量和状态后缀（-idle、-joining、-running）的详细信息，
# 请访问官方文档。

# ========== 信息标牌模板 ==========
info-idle:
- '&6&l⚔ &e<arena-name> &6&l⚔'
- '&7==============='
- '&a状态: &f等待玩家'
- '&b点击加入战斗！'

info-joining:
- '&6&l⚔ &e<arena-name> &6&l⚔'
- '&7==============='
- '&a准备: &f<ready-players>&7/&f<lobby-players>'
- '&e等待开始...'

info-running:
- '&6&l⚔ &e<arena-name> &6&l⚔'
- '&7==============='
- '&c波次: &f<current-wave>'
- '&a存活: &f<live-players>&7/&f<initial-players>'

# ========== 加入标牌模板 ==========
join:
- '&6&l⚔ &e<arena-name> &6&l⚔'
- '&7==============='
- '&a&l点击加入战斗'
- '&b准备好了吗？'

join-running:
- '&6&l⚔ &e<arena-name> &6&l⚔'
- '&7==============='
- '&c&l战斗进行中'
- '&7请等待结束'

# ========== 离开标牌模板 ==========
leave:
- '&6&l⚔ &e<arena-name> &6&l⚔'
- '&7==============='
- '&c&l点击离开'
- '&7确定要退出？'

# ========== 自定义模板 ==========
# 中文风格模板
chinese-style:
- '&c&l【&6<arena-name>&c&l】'
- '&e═══════════════'
- '&a状态: &f等待中'
- '&b点击参与战斗'

chinese-style-joining:
- '&c&l【&6<arena-name>&c&l】'
- '&e═══════════════'
- '&a准备: &f<ready-players>&7/&f<lobby-players>'
- '&e即将开始战斗'

chinese-style-running:
- '&c&l【&6<arena-name>&c&l】'
- '&e═══════════════'
- '&c第&f<current-wave>&c波'
- '&a幸存: &f<live-players>&7/&f<initial-players>'

# 简洁模板
simple:
- '&e<arena-name>'
- '&7-----------'
- '&a点击加入'

simple-running:
- '&e<arena-name>'
- '&7-----------'
- '&c进行中'

# 华丽模板
fancy:
- '&6&l✦ &e&l<arena-name> &6&l✦'
- '&d▬▬▬▬▬▬▬▬▬▬▬▬▬'
- '&a&l⚡ 加入战斗 ⚡'
- '&b&l准备迎接挑战！'

fancy-joining:
- '&6&l✦ &e&l<arena-name> &6&l✦'
- '&d▬▬▬▬▬▬▬▬▬▬▬▬▬'
- '&e准备中 &f<ready-players>&7/&f<lobby-players>'
- '&a&l即将开始！'

fancy-running:
- '&6&l✦ &e&l<arena-name> &6&l✦'
- '&d▬▬▬▬▬▬▬▬▬▬▬▬▬'
- '&c&l第 <current-wave> 波'
- '&a存活 &f<live-players>&7/&f<initial-players>'

# 竞技场专用模板
arena-statue:
- '&5&l◆ &d雕像竞技场 &5&l◆'
- '&7═══════════════'
- '&a&l点击参战'
- '&6挑战无尽波次！'

arena-statue-joining:
- '&5&l◆ &d雕像竞技场 &5&l◆'
- '&7═══════════════'
- '&e准备: &f<ready-players>&7/&f<lobby-players>'
- '&a战斗即将开始'

arena-statue-running:
- '&5&l◆ &d雕像竞技场 &5&l◆'
- '&7═══════════════'
- '&c波次: &f<current-wave>'
- '&a勇士: &f<live-players>&7/&f<initial-players>'
