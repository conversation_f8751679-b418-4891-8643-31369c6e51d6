# MobArena 公式配置 - 雕像竞技场优化版
# 这些公式适用于所有属性。如果全局公式与特定属性公式同名，
# 将使用特定属性公式来解析给定属性。

# ========== 全局公式 ==========
global:
  # 基础公式
  wave-squared:     <current-wave> ^ 2
  wave-inverted:    max(1, <final-wave> - <current-wave>)
  five-each:        <live-players> * 5
  double-team:      <live-players> / 2
  top-up:           max(1, 10 - <live-monsters>)
  dead-man-walking: max(1, <dead-players>)

  # 雕像竞技场专用公式
  statue-basic:     <current-wave> * <live-players> + 2
  statue-advanced:  (<current-wave> ^ 1.5) * max(1, <live-players>)
  statue-elite:     (<current-wave> ^ 2) * <live-players> + <current-wave>
  statue-nightmare: (<current-wave> ^ 2.5) * <live-players> * 2

  # 动态难度调整
  adaptive-easy:    max(3, <current-wave> * 0.8 + <live-players>)
  adaptive-normal:  max(5, <current-wave> * 1.2 + <live-players> * 1.5)
  adaptive-hard:    max(8, <current-wave> * 1.8 + <live-players> * 2)
  adaptive-extreme: max(12, <current-wave> * 2.5 + <live-players> * 3)

  # 生存者奖励公式
  survivor-bonus:   <live-players> * <current-wave> * 2
  last-stand:       max(10, (<dead-players> + 1) * <current-wave>)
  team-spirit:      <live-players> * 3 + <current-wave>

  # 怪物数量平衡
  monster-balance:  max(5, min(50, <current-wave> + <live-players> * 2))
  horde-mode:       <current-wave> * 3 + <live-players> * 5
  endless-swarm:    (<current-wave> ^ 1.3) * <live-players> + 10

# ========== 波次增长公式 ==========
# 这些公式仅适用于默认波次的"growth"属性
wave-growth:
  # 原版公式
  old:    <current-wave> + <initial-players>
  slow:   min(ceil(<initial-players> / 2) + 1, 13) * <current-wave> ^ 0.5
  medium: min(ceil(<initial-players> / 2) + 1, 13) * <current-wave> ^ 0.65
  fast:   min(ceil(<initial-players> / 2) + 1, 13) * <current-wave> ^ 0.8
  psycho: min(ceil(<initial-players> / 2) + 1, 13) * <current-wave> ^ 1.2

  # 雕像竞技场专用增长公式
  statue-gentle:    max(3, ceil(<initial-players> / 2) + 1) * <current-wave> ^ 0.4
  statue-steady:    max(4, ceil(<initial-players> / 2) + 2) * <current-wave> ^ 0.6
  statue-intense:   max(5, ceil(<initial-players> / 2) + 3) * <current-wave> ^ 0.9
  statue-brutal:    max(6, ceil(<initial-players> / 2) + 4) * <current-wave> ^ 1.4
  statue-nightmare: max(8, ceil(<initial-players> / 2) + 5) * <current-wave> ^ 1.8

  # 阶段性增长公式
  early-game:   max(2, <initial-players>) * min(<current-wave> ^ 0.5, 8)
  mid-game:     max(4, <initial-players>) * min(<current-wave> ^ 0.7, 15)
  late-game:    max(6, <initial-players>) * min(<current-wave> ^ 1.0, 25)
  end-game:     max(10, <initial-players>) * min(<current-wave> ^ 1.5, 50)

  # 动态平衡增长
  balanced:     max(3, ceil(<initial-players> / 2) + 2) * (<current-wave> ^ (0.5 + <live-players> * 0.1))
  adaptive:     max(4, <live-players> + 1) * <current-wave> ^ (0.6 + max(0, (<initial-players> - <live-players>) * 0.1))

# ========== 群体波次数量公式 ==========
# 这些公式仅适用于群体波次的"amount"属性
swarm-amount:
  # 原版公式
  low:    max(1, floor(<initial-players> / 2)) * 10
  medium: max(1, floor(<initial-players> / 2)) * 20
  high:   max(1, floor(<initial-players> / 2)) * 30
  psycho: max(1, floor(<initial-players> / 2)) * 60

  # 雕像竞技场群体公式
  statue-mini:     max(5, <live-players> * 3 + <current-wave>)
  statue-small:    max(8, <live-players> * 5 + <current-wave> * 2)
  statue-normal:   max(12, <live-players> * 8 + <current-wave> * 3)
  statue-large:    max(20, <live-players> * 12 + <current-wave> * 5)
  statue-massive:  max(30, <live-players> * 20 + <current-wave> * 8)
  statue-endless:  max(50, <live-players> * 30 + <current-wave> * 12)

  # 波次相关群体公式
  wave-based-low:    max(5, <current-wave> * 2 + <live-players> * 3)
  wave-based-medium: max(10, <current-wave> * 4 + <live-players> * 5)
  wave-based-high:   max(15, <current-wave> * 6 + <live-players> * 8)
  wave-based-extreme: max(25, <current-wave> * 10 + <live-players> * 12)

  # 动态群体公式
  dynamic-swarm:   max(8, (<current-wave> ^ 1.2) + <live-players> * 4)
  escalating:      max(10, <current-wave> * <live-players> + <current-wave>)
  overwhelming:    max(20, (<current-wave> ^ 1.5) * <live-players>)

# ========== Boss血量公式 ==========
# 这些公式仅适用于Boss波次的"health"属性
boss-health:
  # 原版公式
  verylow:  (<initial-players> + 1) * 20 * 4
  low:      (<initial-players> + 1) * 20 * 8
  medium:   (<initial-players> + 1) * 20 * 15
  high:     (<initial-players> + 1) * 20 * 25
  veryhigh: (<initial-players> + 1) * 20 * 40
  psycho:   (<initial-players> + 1) * 20 * 60

  # 雕像竞技场Boss血量公式
  statue-weak:      (<live-players> + 1) * 20 * 6 + <current-wave> * 50
  statue-normal:    (<live-players> + 1) * 20 * 12 + <current-wave> * 100
  statue-strong:    (<live-players> + 1) * 20 * 20 + <current-wave> * 200
  statue-elite:     (<live-players> + 1) * 20 * 35 + <current-wave> * 400
  statue-legendary: (<live-players> + 1) * 20 * 50 + <current-wave> * 800
  statue-mythic:    (<live-players> + 1) * 20 * 80 + <current-wave> * 1500

  # 波次缩放Boss血量
  wave-scaled-low:    (<initial-players> + 1) * 20 * (5 + <current-wave> * 0.5)
  wave-scaled-medium: (<initial-players> + 1) * 20 * (10 + <current-wave> * 1.0)
  wave-scaled-high:   (<initial-players> + 1) * 20 * (20 + <current-wave> * 2.0)
  wave-scaled-extreme: (<initial-players> + 1) * 20 * (40 + <current-wave> * 4.0)

  # 动态Boss血量
  adaptive-boss:    (<live-players> + 1) * 20 * (15 + <current-wave> ^ 1.2)
  escalating-boss:  (<live-players> + 1) * 20 * (25 + <current-wave> ^ 1.5)
  nightmare-boss:   (<live-players> + 1) * 20 * (50 + <current-wave> ^ 2.0)

# ========== 自定义公式类型 ==========
# 奖励倍数公式
reward-multiplier:
  basic:     max(1, <current-wave> * 0.1)
  generous:  max(1, <current-wave> * 0.2 + <live-players> * 0.1)
  epic:      max(1, <current-wave> * 0.5 + <live-players> * 0.2)
  legendary: max(2, <current-wave> * 1.0 + <live-players> * 0.5)

# 经验倍数公式
experience-multiplier:
  normal:   max(1, <current-wave> * 0.05)
  boosted:  max(1, <current-wave> * 0.1 + <live-players> * 0.05)
  enhanced: max(1, <current-wave> * 0.2 + <live-players> * 0.1)
  maximum:  max(2, <current-wave> * 0.5 + <live-players> * 0.2)

# 难度调节公式
difficulty-scaling:
  gentle:    max(0.5, 1 + <current-wave> * 0.02)
  moderate:  max(0.8, 1 + <current-wave> * 0.05)
  intense:   max(1.0, 1 + <current-wave> * 0.1)
  brutal:    max(1.2, 1 + <current-wave> * 0.2)
  nightmare: max(1.5, 1 + <current-wave> * 0.5)
